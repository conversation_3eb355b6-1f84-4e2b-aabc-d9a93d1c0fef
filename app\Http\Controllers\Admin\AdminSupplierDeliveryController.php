<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminSupplierDeliveryController extends Controller
{
    /**
     * Display a listing of supplier deliveries.
     */
    public function index(Request $request)
    {
        $query = SupplierDelivery::with(['supplier', 'product', 'receivedBy']);
        
        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        
        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        // Apply date filter
        $query->whereBetween('delivery_date', [$startDate, $endDate]);
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('supplier', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['delivery_date', 'received_date', 'quantity', 'status', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }
        
        $deliveries = $query->paginate(15);

        // Load returns data for each delivery to calculate returnable quantities
        $deliveries->getCollection()->transform(function ($delivery) {
            // Get approved warehouse returns for this product and supplier
            $approvedReturns = \App\Models\ReturnModel::where('product_id', $delivery->product_id)
                ->where('supplier_id', $delivery->supplier_id)
                ->whereNull('store_id') // Warehouse returns only
                ->where('status', 'approved')
                ->sum('quantity');

            // Calculate max returnable quantity using smart return logic
            $receivedQty = $delivery->received_quantity ?? 0;
            $totalAccountedFor = $receivedQty + $approvedReturns;
            $maxReturnable = max(0, $delivery->quantity - $totalAccountedFor);

            $delivery->approved_returns_quantity = $approvedReturns;
            $delivery->max_returnable_quantity = $maxReturnable;

            return $delivery;
        });

        // Get statistics
        $stats = [
            'total' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'pending' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'pending')->count(),
            'received' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'received')->count(),
            'partial' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'partial')->count(),
        ];
        
        return view('admin.supplier-deliveries.index', compact('deliveries', 'stats', 'filterMonth'));
    }
    
    /**
     * Display the specified delivery.
     */
    public function show(SupplierDelivery $delivery)
    {
        $delivery->load(['supplier', 'product', 'receivedBy']);

        // Calculate returnable quantity for this delivery using smart return logic
        // Get approved warehouse returns for this product and supplier
        $approvedReturns = \App\Models\ReturnModel::where('product_id', $delivery->product_id)
            ->where('supplier_id', $delivery->supplier_id)
            ->whereNull('store_id') // Warehouse returns only
            ->where('status', 'approved')
            ->sum('quantity');

        // Calculate max returnable quantity
        $receivedQty = $delivery->received_quantity ?? 0;
        $totalAccountedFor = $receivedQty + $approvedReturns;
        $maxReturnable = max(0, $delivery->quantity - $totalAccountedFor);

        $delivery->approved_returns_quantity = $approvedReturns;
        $delivery->max_returnable_quantity = $maxReturnable;

        return view('admin.supplier-deliveries.show', compact('delivery'));
    }
    
    /**
     * Receive a supplier delivery.
     */
    public function receive(Request $request, SupplierDelivery $delivery)
    {
        // Only allow receiving if delivery is pending
        if ($delivery->status !== 'pending') {
            return redirect()->route('admin.supplier-deliveries.index')
                ->with('error', 'Pengiriman ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'received_quantity' => 'required|integer|min:1|max:' . $delivery->quantity,
            'notes' => 'nullable|string|max:1000',
        ], [
            'received_quantity.required' => 'Jumlah diterima wajib diisi',
            'received_quantity.integer' => 'Jumlah diterima harus berupa angka bulat',
            'received_quantity.min' => 'Jumlah diterima minimal 1',
            'received_quantity.max' => 'Jumlah diterima tidak boleh melebihi jumlah pengiriman',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($delivery, $validatedData) {
            // Update delivery status
            $status = $validatedData['received_quantity'] == $delivery->quantity ? 'received' : 'partial';
            
            $delivery->update([
                'received_quantity' => $validatedData['received_quantity'],
                'received_date' => now(),
                'status' => $status,
                'notes' => $validatedData['notes'],
                'received_by' => auth()->id(),
            ]);
            
            // Add to warehouse stock
            WarehouseStock::create([
                'product_id' => $delivery->product_id,
                'quantity' => $validatedData['received_quantity'],
                'store_id' => null, // Central warehouse
                'date_received' => now(),
            ]);
            
            // Record stock movement
            $previousStock = WarehouseStock::where('product_id', $delivery->product_id)
                ->whereNull('store_id')
                ->sum('quantity') - $validatedData['received_quantity'];
            
            StockMovement::create([
                'product_id' => $delivery->product_id,
                'type' => 'in',
                'source' => 'supplier',
                'quantity' => $validatedData['received_quantity'],
                'previous_stock' => $previousStock,
                'new_stock' => $previousStock + $validatedData['received_quantity'],
                'reference_type' => 'SupplierDelivery',
                'reference_id' => $delivery->id,
                'notes' => 'Penerimaan dari supplier: ' . $delivery->supplier->name,
                'created_by' => auth()->id(),
            ]);
        });
        
        $message = $delivery->status === 'received' 
            ? 'Pengiriman berhasil diterima sepenuhnya'
            : 'Pengiriman berhasil diterima sebagian';
            
        return redirect()->route('admin.supplier-deliveries.index')
            ->with('success', $message);
    }
    
    /**
     * Cancel a supplier delivery.
     */
    public function cancel(Request $request, SupplierDelivery $delivery)
    {
        // Only allow cancelling if delivery is pending
        if ($delivery->status !== 'pending') {
            return redirect()->route('admin.supplier-deliveries.index')
                ->with('error', 'Pengiriman ini sudah diproses sebelumnya');
        }

        $validatedData = $request->validate([
            'notes' => 'nullable|string|max:1000',
        ], [
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        DB::transaction(function () use ($delivery, $validatedData) {
            // Update delivery status to cancelled
            $delivery->update([
                'status' => 'cancelled',
                'notes' => $validatedData['notes'] ?? 'Pengiriman dibatalkan oleh admin gudang',
                'received_by' => auth()->id(),
            ]);

            // Record stock movement for cancellation tracking
            // Note: Since this system doesn't track supplier inventory,
            // the products are considered "returned" to supplier conceptually
            StockMovement::create([
                'product_id' => $delivery->product_id,
                'type' => 'out',
                'source' => 'supplier',
                'quantity' => -$delivery->quantity,
                'previous_stock' => 0, // No warehouse stock was added
                'new_stock' => 0,
                'reference_type' => 'SupplierDelivery',
                'reference_id' => $delivery->id,
                'notes' => 'Pembatalan pengiriman dari supplier: ' . $delivery->supplier->name .
                          ($validatedData['notes'] ? ' - ' . $validatedData['notes'] : ''),
                'created_by' => auth()->id(),
            ]);
        });

        return redirect()->route('admin.supplier-deliveries.index')
            ->with('success', 'Pengiriman berhasil dibatalkan. Produk dianggap dikembalikan ke supplier.');
    }
}
